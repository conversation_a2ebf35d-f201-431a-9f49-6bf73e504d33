import { Bell, CheckCircle, Zap, Clock, Star, ArrowRight } from 'lucide-react';
import { useEffect, useState, useMemo } from 'react';

const ComingSoon = () => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  // Set launch date (90 days from now)
  const launchDate = useMemo(() => {
    const date = new Date();
    date.setDate(date.getDate() + 90);
    return date;
  }, []);

  const milestones = [
    {
      date: undefined,
      title: 'Beta Platform Launch',
      description: 'Limited beta release with select partners',
      completed: true,
      current: false
    },
    {
      date: undefined,
      title: 'Mobile Apps Release',
      description: 'iOS and Android applications',
      completed: false,
      current: true
    },
    {
      date: undefined,
      title: 'Multi-Language Support',
      description: 'Regional language integration',
      completed: false,
      current: false
    },
    {
      date: undefined,
      title: 'Full Platform Launch',
      description: 'Public release with all core features',
      completed: false,
      current: false
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date().getTime();
      const distance = launchDate.getTime() - now;

      if (distance > 0) {
        setTimeLeft({
          days: Math.floor(distance / (1000 * 60 * 60 * 24)),
          hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((distance % (1000 * 60)) / 1000)
        });
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [launchDate]);

  return (
    <section id="coming-soon" className="py-20 bg-white relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Title */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-6xl font-bold text-highlight mb-6 leading-tight">
            Next-Gen Platform
            <span className="block text-green-600">Coming Soon</span>
          </h2>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
            We're building the most advanced agricultural technology platform. 
            Be the first to experience revolutionary farming solutions that will transform your agricultural operations.
          </p>
        </div>
        {/* Countdown Timer */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-12 max-w-2xl mx-auto">
            {[
              { label: 'Days', value: timeLeft.days },
              { label: 'Hours', value: timeLeft.hours },
              { label: 'Minutes', value: timeLeft.minutes },
              { label: 'Seconds', value: timeLeft.seconds }
            ].map((item, index) => (
              <div
                key={index}
                className="flex flex-col items-center justify-center  bg-agri-cream rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300"
              >
                <div className="text-3xl sm:text-4xl font-bold text-agri-green mb-2">
                  {item.value.toString().padStart(2, '0')}
                </div>
                <div className="text-sm font-medium text-gray-600 uppercase tracking-wide">
                  {item.label}
                </div>
              </div>
            ))}
          </div>
      </div>

      {/* Development Roadmap */}
      <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Development Roadmap</h3>
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              {/* Timeline Line */}
              <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-emerald-200"></div>
              {/* Progress Line for Completed Milestones */}
              <div
                className="absolute left-8 top-0 w-0.5 bg-gradient-to-b from-emerald-500 to-emerald-600 transition-all duration-1000 ease-out"
                style={{
                  height: `${(milestones.filter(m => m.completed).length / milestones.length) * 100}%`
                }}
              ></div>
              {/* Current Progress Line */}
              <div
                className="absolute left-8 w-0.5 bg-gradient-to-b from-amber-400 to-orange-500 transition-all duration-1000 ease-out"
                style={{
                  top: `${(milestones.filter(m => m.completed).length / milestones.length) * 100}%`,
                  height: `${milestones.some(m => m.current) ? (1 / milestones.length) * 50 : 0}%`
                }}
              ></div>
              
              <div className="space-y-8">
                {milestones.map((milestone, index) => (
                  <div key={index} className={`relative flex items-start transition-all duration-500 ${
                    milestone.completed ? 'animate-slide-in' : milestone.current ? 'animate-pulse-once' : 'animate-fade-in-up hover:animate-float'
                  }`}>
                    {/* Timeline Dot */}
                    <div className={`relative z-10 w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 group ${
                      milestone.completed
                        ? 'bg-gradient-to-br from-emerald-500 to-emerald-700 shadow-lg shadow-emerald-500/30 ring-4 ring-emerald-100 animate-glow'
                        : milestone.current
                        ? 'bg-gradient-to-br from-amber-400 to-orange-500 shadow-lg shadow-amber-500/40 ring-4 ring-amber-100'
                        : 'bg-gradient-to-br from-slate-100 to-slate-200 border-4 border-slate-300 hover:border-blue-400 hover:shadow-lg hover:shadow-blue-200/30 hover:from-blue-50 hover:to-blue-100'
                    }`}>
                      {milestone.completed ? (
                        <CheckCircle className="h-8 w-8 text-white drop-shadow-sm" />
                      ) : milestone.current ? (
                        <Zap className="h-8 w-8 text-white drop-shadow-sm" />
                      ) : (
                        <Star className="h-7 w-7 text-slate-500 group-hover:text-blue-600 transition-colors duration-300" />
                      )}
                    </div>

                    {/* Content */}
                    <div className="ml-8 flex-1">
                      <div className={`rounded-2xl p-6 transition-all duration-300 group ${
                        milestone.completed
                          ? 'bg-gradient-to-r from-emerald-50 to-green-50 border-2 border-emerald-200 shadow-md hover:shadow-lg'
                          : milestone.current
                          ? 'bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-200 shadow-md hover:shadow-lg ring-2 ring-amber-100'
                          : 'bg-gradient-to-r from-slate-50 to-blue-50 border-2 border-slate-200 hover:border-blue-300 hover:shadow-xl hover:shadow-blue-100/50 hover:from-blue-50 hover:to-indigo-50'
                      }`}>
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-3">
                            <h4 className={`text-lg font-bold transition-colors duration-300 ${
                              milestone.completed ? 'text-emerald-800' : milestone.current ? 'text-amber-800' : 'text-slate-700 group-hover:text-blue-700'
                            }`}>
                              {milestone.title}
                            </h4>
                            {milestone.completed && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 border border-emerald-200">
                                ✓ Completed
                              </span>
                            )}
                            {milestone.current && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800 border border-amber-200 animate-pulse">
                                <Clock className="w-3 h-3 mr-1" />
                                In Progress
                              </span>
                            )}
                            {!milestone.completed && !milestone.current && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-600 border border-slate-200 group-hover:bg-blue-100 group-hover:text-blue-700 group-hover:border-blue-200 transition-all duration-300">
                                <ArrowRight className="w-3 h-3 mr-1" />
                                Coming Soon
                              </span>
                            )}
                          </div>
                          {milestone.date && (
                            <span className={`text-sm font-medium transition-colors duration-300 ${
                              milestone.completed ? 'text-emerald-700' : milestone.current ? 'text-amber-700' : 'text-slate-500 group-hover:text-blue-600'
                            }`}>
                              {milestone.date}
                            </span>
                          )}
                        </div>
                        <p className={`transition-colors duration-300 ${
                          milestone.completed ? 'text-emerald-700' : milestone.current ? 'text-amber-700' : 'text-slate-600 group-hover:text-blue-600'
                        }`}>
                          {milestone.description}
                        </p>
                        {milestone.completed && (
                          <div className="mt-3 flex items-center text-sm text-emerald-600">
                            <div className="w-2 h-2 bg-emerald-500 rounded-full mr-2 animate-pulse"></div>
                            Successfully delivered
                          </div>
                        )}
                        {milestone.current && (
                          <div className="mt-3 flex items-center text-sm text-amber-600">
                            <div className="w-2 h-2 bg-amber-500 rounded-full mr-2 animate-pulse"></div>
                            Currently in development
                          </div>
                        )}
                        {!milestone.completed && !milestone.current && (
                          <div className="mt-3 flex items-center text-sm text-slate-500 group-hover:text-blue-600 transition-colors duration-300">
                            <div className="w-2 h-2 bg-slate-400 rounded-full mr-2 group-hover:bg-blue-500 transition-colors duration-300"></div>
                            Planned for future release
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

      {/* Notification Signup */}
      <div className="max-w-2xl mx-auto bg-[#FFF5EA] p-8 rounded-xl">
          <div className="flex items-center justify-center mb-6">
            <Bell className="w-8 h-8 text-[#2D4D31] mr-3" />
            <h3 className="text-2xl font-semibold text-black">Get Notified</h3>
          </div>
          
          <p className="text-center text-black opacity-80 mb-6">
            Be the first to know when AgriTram launches. Join our exclusive waitlist.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4">
            <input
              type="email"
              placeholder="Enter your email address"
              className="flex-1 px-6 py-3 rounded-lg border-2 border-[#2D4D31] border-opacity-30 focus:border-[#2D4D31] focus:outline-none bg-white"
            />
            <button className="bg-[#2D4D31] text-white px-8 py-3 rounded-lg border-2 border-[#2D4D31] border-opacity-70 border-dotted hover:bg-transparent hover:text-[#2D4D31] transition-all duration-300">
              Notify Me
            </button>
          </div>
          <p className="text-sm text-center mt-2 text-agri-white/70">
            Join 1,000+ farmers, 10+ traders and 10+ manufacturers already signed up for early access
          </p>
        </div>

      {/* Background Decoration */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-green-200/30 to-transparent rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-blue-200/30 to-transparent rounded-full blur-3xl"></div>
    </section>
  );
};

export default ComingSoon;